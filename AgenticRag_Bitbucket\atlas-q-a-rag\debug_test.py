#!/usr/bin/env python3

import sys
import os
import asyncio
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the app directory to the Python path
sys.path.append(str(Path(__file__).parent))

from app.document_processing.document_processor import DocumentProcessor


async def test_document_processing():
    """Test document processing with debug information"""

    # Initialize processor
    processor = DocumentProcessor()

    # Test file path
    test_file = Path("C:/Users/<USER>/Desktop/test_docs/is_sagligi.pdf")

    print(f"Testing file: {test_file}")
    print(f"File exists: {test_file.exists()}")

    if not test_file.exists():
        print("Test file does not exist!")
        return

    # Test custom metadata
    custom_metadata = {"additionalProp1": {}}

    print(f"Custom metadata: {custom_metadata}")

    try:
        # Process the document
        result = await processor.process_document(
            file_path=test_file,
            collection_name="test_collection",
            custom_metadata=custom_metadata,
        )

        print(f"Processing result: {result}")

    except Exception as e:
        print(f"Error during processing: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_document_processing())
