{"data\\raw\\admin\\httpsmyapi2.atlas.edu.trmevzuat-yonetmelikleryonetmelikistanbul-atlas-universitesi-dis-hekimligi-fakultesi-egitim-ogretim-ve-sinav-yonetmeligi (1).pdf": {"file_name": "httpsmyapi2.atlas.edu.trmevzuat-yonetmelikleryonetmelikistanbul-atlas-universitesi-dis-hekimligi-fakultesi-egitim-ogretim-ve-sinav-yo<PERSON><PERSON><PERSON> (1).pdf", "file_size": 457791, "processed_at": "2025-05-26T15:24:47.684398", "collection_name": "admin_documents", "persist_directory": "data\\chroma_stores\\admin_documents", "chunk_count": 50, "status": "success"}, "data\\all_docs\\mytask\\01_user_and_authentication_system_.md": {"file_name": "01_user_and_authentication_system_.md", "file_size": 25793, "processed_at": "2025-07-07T16:06:19.436966", "collection_name": "mytask_documents", "persist_directory": "data\\chroma_stores\\mytask_documents", "chunk_count": 29, "status": "success"}, "data\\all_docs\\mytask\\02_task_management_core_.md": {"file_name": "02_task_management_core_.md", "file_size": 20862, "processed_at": "2025-07-07T16:06:20.160143", "collection_name": "mytask_documents", "persist_directory": "data\\chroma_stores\\mytask_documents", "chunk_count": 3, "status": "success"}, "data\\all_docs\\mytask\\03_frontend_state_management__redux_toolkit__.md": {"file_name": "03_frontend_state_management__redux_toolkit__.md", "file_size": 28618, "processed_at": "2025-07-07T16:06:21.528832", "collection_name": "mytask_documents", "persist_directory": "data\\chroma_stores\\mytask_documents", "chunk_count": 33, "status": "success"}, "data\\all_docs\\mytask\\04_api_communication__rtk_query__.md": {"file_name": "04_api_communication__rtk_query__.md", "file_size": 27520, "processed_at": "2025-07-07T16:06:22.225751", "collection_name": "mytask_documents", "persist_directory": "data\\chroma_stores\\mytask_documents", "chunk_count": 12, "status": "success"}, "data\\all_docs\\mytask\\05_api_endpoints_and_controllers__backend__.md": {"file_name": "05_api_endpoints_and_controllers__backend__.md", "file_size": 14796, "processed_at": "2025-07-07T16:06:23.567217", "collection_name": "mytask_documents", "persist_directory": "data\\chroma_stores\\mytask_documents", "chunk_count": 18, "status": "success"}, "data\\all_docs\\mytask\\06_atlas_university_integration_.md": {"file_name": "06_atlas_university_integration_.md", "file_size": 23461, "processed_at": "2025-07-07T16:06:25.834948", "collection_name": "mytask_documents", "persist_directory": "data\\chroma_stores\\mytask_documents", "chunk_count": 29, "status": "success"}, "data\\all_docs\\mytask\\07_database_models_.md": {"file_name": "07_database_models_.md", "file_size": 17983, "processed_at": "2025-07-07T16:06:27.067463", "collection_name": "mytask_documents", "persist_directory": "data\\chroma_stores\\mytask_documents", "chunk_count": 21, "status": "success"}, "data\\all_docs\\mytask\\08_internationalization__i18n__.md": {"file_name": "08_internationalization__i18n__.md", "file_size": 22685, "processed_at": "2025-07-07T16:06:28.191771", "collection_name": "mytask_documents", "persist_directory": "data\\chroma_stores\\mytask_documents", "chunk_count": 25, "status": "success"}, "data\\all_docs\\mytask\\index.md": {"file_name": "index.md", "file_size": 2265, "processed_at": "2025-07-07T16:06:28.720363", "collection_name": "mytask_documents", "persist_directory": "data\\chroma_stores\\mytask_documents", "chunk_count": 3, "status": "success"}, "C:\\Users\\<USER>\\Desktop\\WORK\\Health and Safetery  - is saglik - Tayyar Hoca\\1060-bb9f061d-0fbc-406f-9c60-b289762469c1.pdf": {"file_name": "1060-bb9f061d-0fbc-406f-9c60-b289762469c1.pdf", "file_size": 1590927, "processed_at": "2025-07-18T08:01:07.565221", "collection_name": "is_sagligi", "persist_directory": "data\\chroma_stores\\is_sagligi", "chunk_count": 242, "status": "success"}, "C:\\Users\\<USER>\\Desktop\\WORK\\Health and Safetery  - is saglik - Tayyar Hoca\\is-sasligi-ve-guvenligi-siirt-2017103112515511.pdf": {"file_name": "is-sasligi-ve-guvenligi-siirt-2017103112515511.pdf", "file_size": 373421, "processed_at": "2025-07-18T08:01:11.844617", "collection_name": "is_sagligi", "persist_directory": "data\\chroma_stores\\is_sagligi", "chunk_count": 62, "status": "success"}, "C:\\Users\\<USER>\\Desktop\\WORK\\Health and Safetery  - is saglik - Tayyar Hoca\\ISG_TISK.pdf": {"file_name": "ISG_TISK.pdf", "file_size": 1680922, "processed_at": "2025-07-18T08:01:18.879310", "collection_name": "is_sagligi", "persist_directory": "data\\chroma_stores\\is_sagligi", "chunk_count": 243, "status": "success"}, "C:\\Users\\<USER>\\Desktop\\WORK\\Health and Safetery  - is saglik - Tayyar Hoca\\iş sağlığı-3.pdf": {"file_name": "iş sağlığı-3.pdf", "file_size": 1486268, "processed_at": "2025-07-18T08:01:22.378412", "collection_name": "is_sagligi", "persist_directory": "data\\chroma_stores\\is_sagligi", "chunk_count": 68, "status": "success"}, "C:\\Users\\<USER>\\Desktop\\WORK\\Health and Safetery  - is saglik - Tayyar Hoca\\ln-occ-health-safety-final.pdf": {"file_name": "ln-occ-health-safety-final.pdf", "file_size": 2998375, "processed_at": "2025-07-18T08:01:32.593336", "collection_name": "is_sagligi", "persist_directory": "data\\chroma_stores\\is_sagligi", "chunk_count": 437, "status": "success"}, "C:\\Users\\<USER>\\Desktop\\WORK\\Health and Safetery  - is saglik - Tayyar Hoca\\wcms_093550.pdf": {"file_name": "wcms_093550.pdf", "file_size": 1681748, "processed_at": "2025-07-18T08:02:02.212229", "collection_name": "is_sagligi", "persist_directory": "data\\chroma_stores\\is_sagligi", "chunk_count": 594, "status": "success"}, "C:\\Users\\<USER>\\Desktop\\WORK\\Health and Safetery  - is saglik - Tayyar Hoca\\İSG-İş Sağlığı ve Güvenliği.pdf": {"file_name": "İSG-İş Sağlığı ve Güvenliği.pdf", "processed_at": "2025-07-18T08:03:08.877908", "status": "error", "error": "Error code: 400 - {'error': {'message': 'Requested 351716 tokens, max 300000 tokens per request', 'type': 'max_tokens_per_request', 'param': None, 'code': 'max_tokens_per_request'}}"}, "C:\\Users\\<USER>\\Desktop\\test_docs\\is_sagligi.pdf": {"file_name": "is_sagligi.pdf", "file_size": 1486268, "processed_at": "2025-07-18T09:43:18.976375", "collection_name": "is_sagligi_test2", "persist_directory": "data\\chroma_stores\\is_sagligi_test2", "chunk_count": 68, "status": "success"}, "C:\\Users\\<USER>\\Desktop\\WORK\\AgenticRag_Bitbucket\\atlas-q-a-rag\\DEPLOYMENT_GUIDE.md": {"file_name": "DEPLOYMENT_GUIDE.md", "processed_at": "2025-07-18T09:19:06.535509", "status": "error", "error": "'str' object has no attribute 'metadata'"}, "C:\\Users\\<USER>\\Desktop\\WORK\\AgenticRag_Bitbucket\\atlas-q-a-rag\\README.md": {"file_name": "README.md", "processed_at": "2025-07-18T09:19:06.773311", "status": "error", "error": "'str' object has no attribute 'metadata'"}, "C:\\Users\\<USER>\\Desktop\\WORK\\AgenticRag_Bitbucket\\atlas-q-a-rag\\requirements.backend-only.txt": {"file_name": "requirements.backend-only.txt", "processed_at": "2025-07-18T09:19:06.799406", "status": "error", "error": "'str' object has no attribute 'metadata'"}, "C:\\Users\\<USER>\\Desktop\\WORK\\AgenticRag_Bitbucket\\atlas-q-a-rag\\requirements.txt": {"file_name": "requirements.txt", "processed_at": "2025-07-18T09:19:06.824901", "status": "error", "error": "'str' object has no attribute 'metadata'"}, "C:\\Users\\<USER>\\Desktop\\WORK\\AgenticRag_Bitbucket\\atlas-q-a-rag\\test_simple.txt": {"file_name": "test_simple.txt", "processed_at": "2025-07-18T09:19:06.849280", "status": "error", "error": "'str' object has no attribute 'metadata'"}}