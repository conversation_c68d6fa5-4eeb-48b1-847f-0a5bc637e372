"""
Main AgenticRAG class for the Agentic RAG system.
"""

import logging
import os
from typing import Dict, List, Optional, Any, Type

from app.models.bot_config import BotConfig
from app.models.api_models import QueryRequest, QueryResponse, ToolResponse
from app.core.config_loader import Config<PERSON>oader
from app.core.query_router import QueryRouter
from app.tools.base import BaseTool
from app.tools.web_search import WebSearchTool
from app.core.logging_helpers import (
    QueryLogger,
    log_performance,
    get_simple_logger_instance,
)

# Optional imports for database tools
try:
    from app.tools.document_search import DocumentSearchTool

    DOCUMENT_SEARCH_AVAILABLE = True
except ImportError:
    DOCUMENT_SEARCH_AVAILABLE = False
    DocumentSearchTool = None

try:
    from app.tools.mongodb_query import MongoDBQueryTool

    MONGODB_AVAILABLE = True
except ImportError:
    MONGODB_AVAILABLE = False
    MongoDBQueryTool = None

try:
    from app.tools.sql_query import SQLQueryTool

    SQL_AVAILABLE = True
except ImportError:
    SQL_AVAILABLE = False
    SQLQueryTool = None
from app.agents.langgraph_agent import LangGraphAgent

logger = logging.getLogger(__name__)


class AgenticRAG:
    """
    Main class for the Agentic RAG system.
    Handles tool initialization, prompt loading, and query routing.
    """

    # Map of tool types to tool classes (with availability checks)
    @property
    def TOOL_CLASSES(self):
        tool_classes = {
            "WebSearchTool": WebSearchTool,
        }

        if DOCUMENT_SEARCH_AVAILABLE and DocumentSearchTool:
            tool_classes["DocumentSearchTool"] = DocumentSearchTool

        if MONGODB_AVAILABLE and MongoDBQueryTool:
            tool_classes["MongoDBQueryTool"] = MongoDBQueryTool

        if SQL_AVAILABLE and SQLQueryTool:
            tool_classes["SQLQueryTool"] = SQLQueryTool

        return tool_classes

    def __init__(self, config_dir: str = "configs", prompts_dir: str = "prompts"):
        """
        Initialize the AgenticRAG system.

        Args:
            config_dir: Directory containing bot configurations
            prompts_dir: Directory containing prompt templates
        """
        logger.info("Initializing AgenticRAG system...")

        self.config_dir = config_dir
        self.prompts_dir = prompts_dir
        self.simple_logger = get_simple_logger_instance()

        logger.info(f"Config directory: {config_dir}")
        logger.info(f"Prompts directory: {prompts_dir}")

        # Initialize config loader
        with log_performance("Config loader initialization"):
            self.config_loader = ConfigLoader(config_dir)

        self.bots: Dict[str, Dict[str, Any]] = {}

        # Load all bot configurations
        with log_performance("Bot configurations loading"):
            self._load_bots()

        logger.info(f"AgenticRAG system initialized with {len(self.bots)} bots")

    def _load_bots(self) -> None:
        """Load all bot configurations and initialize tools."""
        logger.info("Loading bot configurations...")
        bot_configs = self.config_loader.get_all_configs()
        logger.info(f"Found {len(bot_configs)} bot configurations")

        for bot_name, bot_config in bot_configs.items():
            try:
                logger.info(f"Loading bot: {bot_name}")

                # Initialize tools
                with log_performance(f"Tool initialization for {bot_name}"):
                    tools = self._initialize_tools(bot_config)
                logger.info(f"Initialized {len(tools)} tools for bot: {bot_name}")

                # Load prompts
                with log_performance(f"Prompt loading for {bot_name}"):
                    system_prompt = self._load_prompt(
                        bot_config.prompts.system_prompt_path
                    )
                    query_prompt = self._load_prompt(
                        bot_config.prompts.query_prompt_path
                    )
                logger.info(f"Loaded prompts for bot: {bot_name}")

                # Initialize query router
                with log_performance(f"Query router initialization for {bot_name}"):
                    query_router = QueryRouter(bot_config, tools)

                # Initialize agent
                with log_performance(f"Agent initialization for {bot_name}"):
                    agent = LangGraphAgent(
                        bot_config.agent, system_prompt, query_prompt
                    )

                # Store bot components
                self.bots[bot_name] = {
                    "config": bot_config,
                    "tools": tools,
                    "query_router": query_router,
                    "agent": agent,
                }

                logger.info(f"Successfully loaded bot: {bot_name}")
                # Sade log - bot yüklendi
                tools_count = len([tool for tool in bot_config.tools if tool.enabled])
                self.simple_logger.bot_loaded(bot_name, tools_count)
            except Exception as e:
                logger.error(f"Error loading bot {bot_name}: {str(e)}")
                import traceback

                logger.error(
                    f"Bot loading traceback for {bot_name}: {traceback.format_exc()}"
                )

    def _initialize_tools(self, bot_config: BotConfig) -> Dict[str, BaseTool]:
        """
        Initialize tools for a bot.

        Args:
            bot_config: Bot configuration

        Returns:
            Dictionary of initialized tools
        """
        tools = {}

        for tool_config in bot_config.tools:
            if not tool_config.enabled:
                continue

            tool_class = self.TOOL_CLASSES.get(tool_config.type)
            if not tool_class:
                logger.warning(f"Unknown tool type: {tool_config.type}")
                continue

            try:
                tool = tool_class(tool_config.config)
                tools[tool_config.type] = tool
                logger.info(f"Initialized tool: {tool_config.type}")
            except Exception as e:
                logger.error(f"Error initializing tool {tool_config.type}: {str(e)}")

        return tools

    def _load_prompt(self, prompt_path: str) -> str:
        """
        Load a prompt template.

        Args:
            prompt_path: Path to the prompt template

        Returns:
            The prompt template as a string
        """
        # Check if the path is absolute
        if os.path.isabs(prompt_path):
            full_path = prompt_path
        else:
            # Assume the path is relative to the prompts directory
            full_path = os.path.join(self.prompts_dir, prompt_path)

        try:
            with open(full_path, "r") as f:
                return f.read()
        except Exception as e:
            logger.error(f"Error loading prompt from {full_path}: {str(e)}")
            return "No prompt template available."

    def _ensure_serializable(self, obj: Any) -> Any:
        """
        Ensure an object is JSON serializable.

        Args:
            obj: The object to make serializable

        Returns:
            A JSON serializable version of the object
        """
        if isinstance(obj, dict):
            return {k: self._ensure_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._ensure_serializable(item) for item in obj]
        elif hasattr(obj, "keys") and callable(obj.keys):
            # Convert dict-like objects (like ResultProxy.keys()) to lists
            return list(obj)
        else:
            return obj

    def get_bot_names(self) -> List[str]:
        """
        Get the names of all loaded bots.

        Returns:
            List of bot names
        """
        return list(self.bots.keys())

    def get_bot(self, bot_name: str) -> Optional[Dict[str, Any]]:
        """
        Get a bot by name.

        Args:
            bot_name: Name of the bot

        Returns:
            Bot components or None if not found
        """
        return self.bots.get(bot_name)

    async def process_query(
        self, bot_name: str, request: QueryRequest
    ) -> QueryResponse:
        """
        Process a query for a specific bot.

        Args:
            bot_name: Name of the bot
            request: Query request

        Returns:
            Query response
        """
        # Initialize query logger
        query_logger_instance = QueryLogger(bot_name=bot_name)

        logger.info(f"Processing query for bot: {bot_name}")
        logger.debug(
            f"Query: {request.query[:100]}{'...' if len(request.query) > 100 else ''}"
        )

        bot = self.get_bot(bot_name)
        if not bot:
            error_msg = f"Bot not found: {bot_name}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        try:
            # Route the query to tools
            logger.info(f"Routing query to tools for bot: {bot_name}")
            query_router: QueryRouter = bot["query_router"]

            with log_performance(f"Tool routing for {bot_name}"):
                tool_results = await query_router.route_query(
                    request.query, **request.metadata or {}
                )

            # Log tool selection
            selected_tools = tool_results.get("selected_tools", [])
            reasoning = tool_results.get("tool_selection_reasoning", "")
            query_logger_instance.log_tool_selection(selected_tools, reasoning)

            # Sade log - araçlar seçildi
            self.simple_logger.tools_selected(bot_name, selected_tools)

            logger.info(
                f"Selected tools for bot {bot_name}: {', '.join(selected_tools)}"
            )

            # Process the query with the agent
            logger.info(f"Processing query with agent for bot: {bot_name}")
            agent: LangGraphAgent = bot["agent"]

            with log_performance(f"Agent processing for {bot_name}"):
                agent_response = await agent.process_query(
                    request.query,
                    tool_results["tool_responses"],
                    session_id=request.session_id,
                )

            logger.info(f"Agent processing completed for bot: {bot_name}")

            # Format tool responses
            tool_responses = []
            for tool_name, result in tool_results["tool_responses"].items():
                # Ensure the result is JSON serializable
                serializable_result = self._ensure_serializable(result)
                tool_responses.append(
                    ToolResponse(
                        tool_name=tool_name, content=serializable_result, metadata={}
                    )
                )

                # Log tool result summary
                result_summary = self._get_result_summary(result)
                query_logger_instance.log_tool_result(tool_name, result_summary, 0)

                # Sade log - araç çalıştırıldı
                self.simple_logger.tool_executed(tool_name, 0, True, result_summary)

            # Create the response metadata
            metadata = {}
            if agent_response.get("error"):
                metadata["error"] = agent_response.get("error")

            # Add isactive status to metadata
            if hasattr(bot["config"], "isactive"):
                metadata["isactive"] = bot["config"].isactive

            # Create the response
            response = QueryResponse(
                bot_name=bot_name,
                query=request.query,
                response=agent_response["response"],
                tool_responses=tool_responses,
                selected_tools=tool_results.get("selected_tools"),
                tool_selection_reasoning=tool_results.get("tool_selection_reasoning"),
                raw_llm_output=tool_results.get("raw_llm_output"),
                session_id=request.session_id,
                execution_time=None,  # Will be set in main.py if bot is in test mode
                metadata=metadata,
            )

            # Log response generation
            response_length = len(agent_response["response"])
            query_logger_instance.log_response_generation(response_length, 0)

            logger.info(
                f"Query processed successfully for bot: {bot_name}, response length: {response_length}"
            )

            return response
        except Exception as e:
            error_msg = f"Error processing query for bot {bot_name}: {str(e)}"
            logger.error(error_msg)
            import traceback

            logger.error(f"Query processing traceback: {traceback.format_exc()}")
            raise

    def _get_result_summary(self, result: Any) -> str:
        """
        Get a summary of tool result for logging.

        Args:
            result: Tool result

        Returns:
            Summary string
        """
        if isinstance(result, dict):
            if "results" in result:
                return f"Found {len(result['results'])} results"
            elif "data" in result:
                return f"Returned data with {len(result['data'])} items"
            else:
                return f"Returned dict with {len(result)} keys"
        elif isinstance(result, list):
            return f"Returned {len(result)} items"
        elif isinstance(result, str):
            return f"Returned text ({len(result)} chars)"
        else:
            return f"Returned {type(result).__name__}"
