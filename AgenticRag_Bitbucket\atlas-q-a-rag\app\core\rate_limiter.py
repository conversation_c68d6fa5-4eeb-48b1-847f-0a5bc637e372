"""
Simple in-memory rate limiter for bot-specific rate limiting.
"""

import time
import logging
from typing import Dict, <PERSON><PERSON>
from collections import defaultdict, deque
from fastapi import HTTPException

from app.core.logging_config import get_logger

logger = get_logger(__name__)


class SimpleRateLimiter:
    """
    Simple in-memory rate limiter using sliding window approach.
    """

    def __init__(self):
        # Dictionary to store request timestamps for each key
        # Format: {key: deque([timestamp1, timestamp2, ...])}
        self.requests: Dict[str, deque] = defaultdict(deque)

    def is_allowed(
        self, key: str, max_requests: int, window_seconds: int
    ) -> Tuple[bool, int]:
        """
        Check if a request is allowed based on rate limits.

        Args:
            key: Unique identifier for the client (e.g., "bot_name:session_id")
            max_requests: Maximum number of requests allowed in the window
            window_seconds: Time window in seconds

        Returns:
            Tuple of (is_allowed, retry_after_seconds)
        """
        current_time = time.time()
        window_start = current_time - window_seconds

        # Get request history for this key
        request_history = self.requests[key]

        # Remove old requests outside the window
        while request_history and request_history[0] < window_start:
            request_history.popleft()

        # Check if we're within the limit
        if len(request_history) < max_requests:
            # Add current request timestamp
            request_history.append(current_time)
            return True, 0
        else:
            # Calculate retry after time (when the oldest request will expire)
            oldest_request = request_history[0]
            retry_after = int(oldest_request + window_seconds - current_time) + 1
            return False, max(retry_after, 1)

    def cleanup_old_entries(self, max_age_seconds: int = 3600):
        """
        Clean up old entries to prevent memory leaks.
        Should be called periodically.
        """
        current_time = time.time()
        cutoff_time = current_time - max_age_seconds

        keys_to_remove = []
        for key, request_history in self.requests.items():
            # Remove old requests
            while request_history and request_history[0] < cutoff_time:
                request_history.popleft()

            # If no recent requests, mark key for removal
            if not request_history:
                keys_to_remove.append(key)

        # Remove empty keys
        for key in keys_to_remove:
            del self.requests[key]

        if keys_to_remove:
            logger.debug(f"Cleaned up {len(keys_to_remove)} old rate limit entries")


# Global rate limiter instance
_rate_limiter = SimpleRateLimiter()


def get_rate_limiter() -> SimpleRateLimiter:
    """Get the global rate limiter instance."""
    return _rate_limiter


def check_rate_limit(key: str, max_requests: int, window_seconds: int) -> None:
    """
    Check rate limit and raise HTTPException if exceeded.

    Args:
        key: Unique identifier for the client
        max_requests: Maximum number of requests allowed
        window_seconds: Time window in seconds

    Raises:
        HTTPException: If rate limit is exceeded
    """
    limiter = get_rate_limiter()
    is_allowed, retry_after = limiter.is_allowed(key, max_requests, window_seconds)

    if not is_allowed:
        logger.warning(f"Rate limit exceeded for key: {key}")
        raise HTTPException(
            status_code=429,
            detail={
                "error": "Rate limit exceeded",
                "message": f"Too many requests. Please wait {retry_after} seconds before trying again.",
                "retry_after": retry_after,
                "limit": f"{max_requests} requests per {window_seconds} seconds",
            },
        )


def parse_rate_limit_string(rate_limit_str: str) -> Tuple[int, int]:
    """
    Parse rate limit string like "10/minute" or "100/hour" into (max_requests, window_seconds).

    Args:
        rate_limit_str: Rate limit string (e.g., "10/minute", "100/hour")

    Returns:
        Tuple of (max_requests, window_seconds)
    """
    try:
        parts = rate_limit_str.split("/")
        if len(parts) != 2:
            raise ValueError("Invalid rate limit format")

        max_requests = int(parts[0])
        time_unit = parts[1].lower()

        time_multipliers = {"second": 1, "minute": 60, "hour": 3600, "day": 86400}

        if time_unit not in time_multipliers:
            raise ValueError(f"Unknown time unit: {time_unit}")

        window_seconds = time_multipliers[time_unit]
        return max_requests, window_seconds

    except (ValueError, IndexError) as e:
        logger.error(f"Error parsing rate limit string '{rate_limit_str}': {str(e)}")
        # Return default values
        return 10, 60  # 10 requests per minute


def check_bot_rate_limit(bot_name: str, session_id: str, rate_limit_config) -> None:
    """
    Check rate limit for a specific bot and session.

    Args:
        bot_name: Name of the bot
        session_id: Session ID of the user
        rate_limit_config: Rate limit configuration object

    Raises:
        HTTPException: If rate limit is exceeded
    """
    logger.info(f"Checking rate limit for bot: {bot_name}, session: {session_id}")
    logger.info(f"Rate limit config: {rate_limit_config}")

    if not rate_limit_config or not rate_limit_config.enabled:
        logger.info(f"Rate limiting disabled for bot: {bot_name}")
        return

    # Create unique key for this bot and session
    key = f"{bot_name}:{session_id or 'anonymous'}"

    # Check minute limit
    max_requests_per_minute = rate_limit_config.requests_per_minute
    check_rate_limit(key + ":minute", max_requests_per_minute, 60)

    # Check hour limit
    max_requests_per_hour = rate_limit_config.requests_per_hour
    check_rate_limit(key + ":hour", max_requests_per_hour, 3600)

    logger.debug(f"Rate limit check passed for bot: {bot_name}, session: {session_id}")
